#!/usr/bin/env python3
"""
Test script to verify the new features in VibroML:
1. Renamed random method to opt_random
2. Automatic method suffix in output folder names
3. Custom prefix functionality
"""

import os
import sys
import tempfile
import time
from unittest.mock import Mock, patch

# Add the vibroml package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_opt_random_method_name():
    """Test that the random method has been renamed to opt_random."""
    print("Testing opt_random method name...")
    
    try:
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test that 'opt_random' is in the choices for --method
        method_action = None
        for action in parser._actions:
            if action.dest == 'method':
                method_action = action
                break
        
        if method_action is None:
            print("  ❌ Method argument not found in parser")
            return False
        
        if 'opt_random' not in method_action.choices:
            print(f"  ❌ 'opt_random' not found in method choices: {method_action.choices}")
            return False
        
        # Test that old 'random' is NOT in choices
        if 'random' in method_action.choices:
            print(f"  ❌ Old 'random' method still found in choices: {method_action.choices}")
            return False
        
        print("  ✅ 'opt_random' method found in CLI choices, old 'random' removed")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing opt_random method name: {e}")
        return False

def test_output_prefix_argument():
    """Test that the --output-prefix argument is available."""
    print("Testing --output-prefix argument...")
    
    try:
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test that output-prefix argument exists
        output_prefix_action = None
        for action in parser._actions:
            if action.dest == 'output_prefix':
                output_prefix_action = action
                break
        
        if output_prefix_action is None:
            print("  ❌ --output-prefix argument not found in parser")
            return False
        
        # Test parsing with output-prefix
        args = parser.parse_args(['--cif', 'test.cif', '--output-prefix', 'test_prefix'])
        if args.output_prefix != 'test_prefix':
            print(f"  ❌ --output-prefix not parsed correctly: {args.output_prefix}")
            return False
        
        print("  ✅ --output-prefix argument found and working correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing --output-prefix argument: {e}")
        return False

def test_method_suffix_mapping():
    """Test that method suffixes are correctly mapped."""
    print("Testing method suffix mapping...")
    
    try:
        # Test the method suffix mapping logic
        method_suffix_map = {
            "traditional": "_TRADITIONAL",
            "ga": "_GA", 
            "traditional_all": "_TRADITIONAL_ALL",
            "opt_random": "_OPT_RANDOM"
        }
        
        expected_methods = ["traditional", "ga", "traditional_all", "opt_random"]
        for method in expected_methods:
            if method not in method_suffix_map:
                print(f"  ❌ Method '{method}' not found in suffix mapping")
                return False
        
        # Test that old 'random' is not in mapping
        if 'random' in method_suffix_map:
            print("  ❌ Old 'random' method still found in suffix mapping")
            return False
        
        print("  ✅ Method suffix mapping is correct")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing method suffix mapping: {e}")
        return False

def test_output_folder_naming():
    """Test the output folder naming logic with different scenarios."""
    print("Testing output folder naming logic...")
    
    try:
        # Mock the main.py logic for testing
        cif_filename_base = "test_structure"
        timestamp = "20250901-120000"
        
        # Test case 1: GA method without prefix
        method = "ga"
        method_suffix_map = {
            "traditional": "_TRADITIONAL",
            "ga": "_GA", 
            "traditional_all": "_TRADITIONAL_ALL",
            "opt_random": "_OPT_RANDOM"
        }
        method_suffix = method_suffix_map.get(method, "")
        prefix_part = ""
        
        expected_name = f"{prefix_part}{cif_filename_base}{method_suffix}_phonon_output_{timestamp}"
        actual_name = f"{prefix_part}{cif_filename_base}{method_suffix}_phonon_output_{timestamp}"
        
        if actual_name != "test_structure_GA_phonon_output_20250901-120000":
            print(f"  ❌ GA method naming incorrect: {actual_name}")
            return False
        
        # Test case 2: opt_random method with prefix
        method = "opt_random"
        method_suffix = method_suffix_map.get(method, "")
        prefix_part = "experiment1_"
        
        expected_name = f"{prefix_part}{cif_filename_base}{method_suffix}_phonon_output_{timestamp}"
        actual_name = f"{prefix_part}{cif_filename_base}{method_suffix}_phonon_output_{timestamp}"
        
        if actual_name != "experiment1_test_structure_OPT_RANDOM_phonon_output_20250901-120000":
            print(f"  ❌ opt_random method with prefix naming incorrect: {actual_name}")
            return False
        
        # Test case 3: traditional_all method without prefix
        method = "traditional_all"
        method_suffix = method_suffix_map.get(method, "")
        prefix_part = ""
        
        expected_name = f"{prefix_part}{cif_filename_base}{method_suffix}_phonon_output_{timestamp}"
        actual_name = f"{prefix_part}{cif_filename_base}{method_suffix}_phonon_output_{timestamp}"
        
        if actual_name != "test_structure_TRADITIONAL_ALL_phonon_output_20250901-120000":
            print(f"  ❌ traditional_all method naming incorrect: {actual_name}")
            return False
        
        print("  ✅ Output folder naming logic works correctly for all scenarios")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing output folder naming: {e}")
        return False

def test_backward_compatibility():
    """Test that existing functionality still works."""
    print("Testing backward compatibility...")
    
    try:
        from vibroml.utils.utils import get_arg_parser_and_settings
        
        parser, settings = get_arg_parser_and_settings()
        
        # Test that all existing methods still work
        existing_methods = ["ga", "traditional", "traditional_all"]
        method_action = None
        for action in parser._actions:
            if action.dest == 'method':
                method_action = action
                break
        
        for method in existing_methods:
            if method not in method_action.choices:
                print(f"  ❌ Existing method '{method}' not found in choices")
                return False
        
        # Test parsing without output-prefix (should default to None)
        args = parser.parse_args(['--cif', 'test.cif', '--method', 'ga'])
        if hasattr(args, 'output_prefix') and args.output_prefix is not None:
            print(f"  ❌ output_prefix should default to None, got: {args.output_prefix}")
            return False
        
        print("  ✅ Backward compatibility maintained")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing backward compatibility: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing New VibroML Features")
    print("=" * 60)

    tests = [
        test_opt_random_method_name,
        test_output_prefix_argument,
        test_method_suffix_mapping,
        test_output_folder_naming,
        test_backward_compatibility,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        print(f"\n{test_func.__name__.replace('_', ' ').title()}:")
        try:
            if test_func():
                passed += 1
                print("  ✅ PASSED")
            else:
                print("  ❌ FAILED")
        except Exception as e:
            print(f"  ❌ FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"Test Results: {passed}/{total} tests passed")
    print(f"{'='*60}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
