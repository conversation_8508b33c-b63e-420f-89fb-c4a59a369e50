#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to organize MEIDNet structures and submit phonon calculations.
This script will:
1. Create organized directory structure for each CIF file
2. Move and rename CIF files with material_composition_lattice_parameter format
3. Copy and customize job scripts
4. Submit batch jobs using sbatch
"""

import os
import shutil
import subprocess
import re
from pathlib import Path

def extract_cif_info(cif_path):
    """Extract material composition and lattice parameter from CIF file."""
    with open(cif_path, 'r') as f:
        content = f.read()
    
    # Extract material composition from data_ line
    data_match = re.search(r'^data_(.+)$', content, re.MULTILINE)
    if not data_match:
        raise ValueError(f"Could not find data_ line in {cif_path}")
    composition = data_match.group(1)
    
    # Extract lattice parameter from _cell_length_a line
    lattice_match = re.search(r'^_cell_length_a\s+([0-9.]+)', content, re.MULTILINE)
    if not lattice_match:
        raise ValueError(f"Could not find lattice parameter in {cif_path}")
    lattice_param = float(lattice_match.group(1))
    
    return composition, lattice_param

def format_lattice_param(lattice_param):
    """Format lattice parameter for directory/file naming (replace . with p)."""
    return f"{lattice_param:.5f}".replace('.', 'p')

def process_directory(base_dir):
    """Process all CIF files in the given directory."""
    base_path = Path(base_dir)
    script_template = base_path / "phonon_gpu_example.sh"
    
    if not script_template.exists():
        raise FileNotFoundError(f"Template script not found: {script_template}")
    
    # Find all subdirectories with CIF files
    subdirs = [d for d in base_path.iterdir() if d.is_dir()]
    
    job_scripts = []  # List to store paths of job scripts to submit
    
    for subdir in subdirs:
        print(f"\nProcessing directory: {subdir.name}")
        
        # Find all CIF files in this subdirectory
        cif_files = list(subdir.glob("*.cif"))
        
        for cif_file in cif_files:
            print(f"  Processing: {cif_file.name}")
            
            try:
                # Extract information from CIF file
                composition, lattice_param = extract_cif_info(cif_file)
                formatted_lattice = format_lattice_param(lattice_param)
                
                # Create new directory name
                new_dir_name = f"{composition}_{formatted_lattice}"
                new_dir_path = subdir / new_dir_name
                
                print(f"    Composition: {composition}")
                print(f"    Lattice parameter: {lattice_param:.5f} Å")
                print(f"    Creating directory: {new_dir_name}")
                
                # Create new directory
                new_dir_path.mkdir(exist_ok=True)
                
                # New CIF filename
                new_cif_name = f"{composition}_{formatted_lattice}.cif"
                new_cif_path = new_dir_path / new_cif_name
                
                # Move and rename CIF file
                shutil.move(str(cif_file), str(new_cif_path))
                print(f"    Moved CIF to: {new_cif_path}")
                
                # Copy and customize job script
                new_script_path = new_dir_path / "phonon_gpu_example.sh"
                shutil.copy2(str(script_template), str(new_script_path))
                
                # Modify the script to reference the correct CIF file
                with open(new_script_path, 'r') as f:
                    script_content = f.read()
                
                # Replace xxx.cif with the actual CIF filename
                modified_content = script_content.replace('xxx.cif', new_cif_name)
                
                with open(new_script_path, 'w') as f:
                    f.write(modified_content)
                
                print(f"    Created and customized script: {new_script_path}")
                
                # Add to list of scripts to submit
                job_scripts.append(new_script_path)
                
            except Exception as e:
                print(f"    ERROR processing {cif_file.name}: {e}")
                continue
    
    return job_scripts

def submit_jobs(job_scripts):
    """Submit all job scripts using sbatch."""
    print(f"\n{'='*60}")
    print("SUBMITTING JOBS")
    print(f"{'='*60}")
    
    submitted_jobs = []
    failed_jobs = []
    
    for script_path in job_scripts:
        try:
            # Change to the script's directory before submitting
            script_dir = script_path.parent
            script_name = script_path.name
            
            print(f"\nSubmitting job from directory: {script_dir}")
            print(f"Script: {script_name}")
            
            # Submit job using sbatch
            result = subprocess.run(
                ['sbatch', script_name],
                cwd=str(script_dir),
                capture_output=True,
                text=True,
                check=True
            )
            
            job_id = result.stdout.strip()
            print(f"  SUCCESS: {job_id}")
            submitted_jobs.append((script_path, job_id))
            
        except subprocess.CalledProcessError as e:
            print(f"  ERROR: Failed to submit {script_path}")
            print(f"    Return code: {e.returncode}")
            print(f"    STDOUT: {e.stdout}")
            print(f"    STDERR: {e.stderr}")
            failed_jobs.append(script_path)
        except Exception as e:
            print(f"  ERROR: Unexpected error submitting {script_path}: {e}")
            failed_jobs.append(script_path)
    
    # Summary
    print(f"\n{'='*60}")
    print("SUBMISSION SUMMARY")
    print(f"{'='*60}")
    print(f"Successfully submitted: {len(submitted_jobs)} jobs")
    print(f"Failed submissions: {len(failed_jobs)} jobs")
    
    if submitted_jobs:
        print("\nSuccessfully submitted jobs:")
        for script_path, job_id in submitted_jobs:
            print(f"  {script_path.parent.name}/{script_path.name} -> {job_id}")
    
    if failed_jobs:
        print("\nFailed job submissions:")
        for script_path in failed_jobs:
            print(f"  {script_path}")

def main():
    """Main function to organize structures and submit jobs."""
    base_dir = "examples/MEIDNet_structures_round2"
    
    print("MEIDNet Structure Organization and Job Submission")
    print(f"{'='*60}")
    print(f"Base directory: {base_dir}")
    
    try:
        # Process all directories and get list of job scripts
        job_scripts = process_directory(base_dir)
        
        if not job_scripts:
            print("\nNo job scripts were created. Exiting.")
            return
        
        print(f"\nCreated {len(job_scripts)} job scripts")
        
        # Ask for confirmation before submitting jobs
        print(f"\n{'='*60}")
        print("READY TO SUBMIT JOBS")
        print(f"{'='*60}")
        print(f"About to submit {len(job_scripts)} jobs to the scheduler.")
        
        response = input("Do you want to proceed with job submission? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            submit_jobs(job_scripts)
        else:
            print("Job submission cancelled.")
            print("Job scripts have been created and are ready for manual submission.")
            print("\nTo submit manually, run:")
            for script in job_scripts:
                print(f"  cd {script.parent} && sbatch {script.name}")
    
    except Exception as e:
        print(f"ERROR: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
