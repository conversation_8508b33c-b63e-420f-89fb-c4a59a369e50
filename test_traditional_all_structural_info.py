#!/usr/bin/env python3
"""
Test script to verify that traditional_all mode properly collects and displays
structural information (number of atoms, space group symbol, crystal system)
in both individual iteration summaries and overall summaries.
"""

import os
import sys
import tempfile
from unittest.mock import Mock, patch

# Add the vibroml package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_traditional_all_data_collection():
    """Test that traditional_all mode properly collects structural information."""
    print("Testing traditional_all structural information data collection...")
    
    try:
        # Mock the relaxation results with structural information
        mock_relaxation_result = {
            'energy_per_atom': -5.123456,
            'relaxed_atoms': Mock(),
            'num_atoms': 8,
            'international_symbol': 'Fm-3m',
            'crystal_system': 'cubic'
        }
        
        # Test that the sample_result dictionary includes structural information
        # This simulates the fixed code in traditional_all mode
        sample_result = {
            'sample_id': 1,
            'energy_per_atom': mock_relaxation_result['energy_per_atom'],
            'relaxed_atoms': mock_relaxation_result['relaxed_atoms'],
            'params': (0.5, 0.8, [1.0, 1.0, 1.0, 0.0, 0.0, 0.0]),
            'iteration': 1,
            'pairing': 'test_pairing',
            'configuration': 'Test configuration',
            # Structural information should be included
            'num_atoms': mock_relaxation_result.get('num_atoms', 'N/A'),
            'international_symbol': mock_relaxation_result.get('international_symbol', 'N/A'),
            'crystal_system': mock_relaxation_result.get('crystal_system', 'N/A'),
            # Soft mode information
            'primary_mode': {
                'label': 'M',
                'k_point': [0.5, 0.5, 0.0],
                'frequency': -2.5,
                'band_index': 0
            },
            'secondary_mode': {
                'label': 'X',
                'k_point': [0.5, 0.0, 0.0],
                'frequency': -1.8,
                'band_index': 1
            }
        }
        
        # Verify that structural information is present
        required_keys = ['num_atoms', 'international_symbol', 'crystal_system']
        for key in required_keys:
            if key not in sample_result:
                print(f"  ❌ Missing key '{key}' in sample_result")
                return False
            if sample_result[key] == 'N/A':
                print(f"  ❌ Key '{key}' has default 'N/A' value instead of actual data")
                return False
        
        # Verify the values are correct
        if sample_result['num_atoms'] != 8:
            print(f"  ❌ Incorrect num_atoms: expected 8, got {sample_result['num_atoms']}")
            return False
        
        if sample_result['international_symbol'] != 'Fm-3m':
            print(f"  ❌ Incorrect international_symbol: expected 'Fm-3m', got {sample_result['international_symbol']}")
            return False
        
        if sample_result['crystal_system'] != 'cubic':
            print(f"  ❌ Incorrect crystal_system: expected 'cubic', got {sample_result['crystal_system']}")
            return False
        
        print("  ✅ Structural information properly collected in sample_result")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing traditional_all data collection: {e}")
        return False

def test_summary_format():
    """Test that the summary format includes structural information columns."""
    print("Testing traditional_all summary format...")
    
    try:
        # Test the overall summary header format
        expected_overall_header = f"{'Num Atoms':<12} {'Int. Symbol':<15} {'Crystal System':<18} {'Energy (eV/atom)':<25} {'Iter':<5} {'Sample':<8} {'Primary Mode':<25} {'Secondary Mode':<25} {'Configuration Description':<80} {'Parameters':<50} {'Soft Mode Details':<80}"
        
        # Check that the header contains the structural information columns
        if 'Num Atoms' not in expected_overall_header:
            print("  ❌ 'Num Atoms' column missing from overall summary header")
            return False
        
        if 'Int. Symbol' not in expected_overall_header:
            print("  ❌ 'Int. Symbol' column missing from overall summary header")
            return False
        
        if 'Crystal System' not in expected_overall_header:
            print("  ❌ 'Crystal System' column missing from overall summary header")
            return False
        
        # Test the iteration summary header format
        expected_iteration_header = f"{'Sample ID':<10} {'Num Atoms':<12} {'Int. Symbol':<15} {'Crystal System':<18} {'Energy (eV/atom)':<20} {'Status':<10} {'Configuration Description':<80} {'Failure Reason':<30}"
        
        # Check that the iteration header contains the structural information columns
        if 'Num Atoms' not in expected_iteration_header:
            print("  ❌ 'Num Atoms' column missing from iteration summary header")
            return False
        
        if 'Int. Symbol' not in expected_iteration_header:
            print("  ❌ 'Int. Symbol' column missing from iteration summary header")
            return False
        
        if 'Crystal System' not in expected_iteration_header:
            print("  ❌ 'Crystal System' column missing from iteration summary header")
            return False
        
        print("  ✅ Summary format includes structural information columns")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing summary format: {e}")
        return False

def test_data_formatting():
    """Test that structural information is properly formatted in summary output."""
    print("Testing traditional_all data formatting...")
    
    try:
        # Mock result data with structural information
        mock_result = {
            'num_atoms': 16,
            'international_symbol': 'P1',
            'crystal_system': 'triclinic',
            'energy_per_atom': -4.567890,
            'iteration': 2,
            'sample_id': 5,
            'primary_mode': {
                'label': 'Gamma',
                'k_point': [0.0, 0.0, 0.0],
                'frequency': -3.2,
                'band_index': 0
            },
            'secondary_mode': {
                'label': 'M',
                'k_point': [0.5, 0.5, 0.0],
                'frequency': -1.5,
                'band_index': 1
            },
            'configuration': 'Test configuration with long description',
            'params': (0.3, 1.2, [1.1, 0.9, 1.0, 0.1, -0.05, 0.02])
        }
        
        # Test formatting of structural information
        num_atoms_str = str(mock_result['num_atoms'])
        international_symbol_str = mock_result['international_symbol']
        crystal_system_str = mock_result['crystal_system']
        energy_str = f"{mock_result['energy_per_atom']:.6f}"
        
        # Verify formatting
        if len(num_atoms_str) > 12:  # Column width check
            print(f"  ❌ num_atoms string too long: {num_atoms_str}")
            return False
        
        if len(international_symbol_str) > 15:  # Column width check
            print(f"  ❌ international_symbol string too long: {international_symbol_str}")
            return False
        
        if len(crystal_system_str) > 18:  # Column width check
            print(f"  ❌ crystal_system string too long: {crystal_system_str}")
            return False
        
        # Test that energy formatting works
        if not energy_str.startswith('-4.567890'):
            print(f"  ❌ Energy formatting incorrect: {energy_str}")
            return False
        
        print("  ✅ Data formatting works correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing data formatting: {e}")
        return False

def test_backward_compatibility():
    """Test that the changes don't break existing functionality."""
    print("Testing backward compatibility...")
    
    try:
        # Test handling of missing structural information
        mock_result_missing_info = {
            'energy_per_atom': -3.456789,
            'sample_id': 1,
            'iteration': 1,
            'configuration': 'Test config',
            'params': (0.5, 1.0, [1.0, 1.0, 1.0, 0.0, 0.0, 0.0])
            # Missing: num_atoms, international_symbol, crystal_system
        }
        
        # Test that .get() with default values works
        num_atoms = mock_result_missing_info.get('num_atoms', 'N/A')
        international_symbol = mock_result_missing_info.get('international_symbol', 'N/A')
        crystal_system = mock_result_missing_info.get('crystal_system', 'N/A')
        
        if num_atoms != 'N/A':
            print(f"  ❌ Default value for num_atoms not working: {num_atoms}")
            return False
        
        if international_symbol != 'N/A':
            print(f"  ❌ Default value for international_symbol not working: {international_symbol}")
            return False
        
        if crystal_system != 'N/A':
            print(f"  ❌ Default value for crystal_system not working: {crystal_system}")
            return False
        
        print("  ✅ Backward compatibility maintained")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing backward compatibility: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 70)
    print("Testing Traditional_All Mode Structural Information")
    print("=" * 70)

    tests = [
        test_traditional_all_data_collection,
        test_summary_format,
        test_data_formatting,
        test_backward_compatibility,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        print(f"\n{test_func.__name__.replace('_', ' ').title()}:")
        try:
            if test_func():
                passed += 1
                print("  ✅ PASSED")
            else:
                print("  ❌ FAILED")
        except Exception as e:
            print(f"  ❌ FAILED with exception: {e}")
    
    print(f"\n{'='*70}")
    print(f"Test Results: {passed}/{total} tests passed")
    print(f"{'='*70}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
